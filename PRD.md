  # Pixiv Tag Downloader (Rust 版本) - 需求文档

  ## 1. 项目概述

  本项目旨在开发一个 **Rust 应用程序**，用于根据用户指定的Pixiv用户ID (UID) 下载其作品（包括图片、插画、漫画、小说）。程序需通过读取本地 `cookie.txt` 文件进行登录，并提供交互式界面或命令行参数供用户指定需要下载的作品标签及其他选项。下载过程应采用 **Rust 的异步机制 (如 `tokio` 或 `async-std`) 或并行处理库 (如 `rayon`)** 以提高效率，并加入可配置的随机延迟以避免触发Pixiv的访问频率限制。下载的内容需按照用户可高度自定义的目录结构和文件命名规则进行组织和保存。图片/漫画类作品将生成包含元数据的同名（或指定名称的）TXT文件；小说则直接保存为包含元数据和内容的TXT文件。

  程序应支持多种使用方式：交互式命令行操作、通过命令行参数直接执行任务。程序需支持多种下载方法，包括直接下载和通过 Aria2（支持命令行调用及通过WSS协议的RPC方式）下载。项目强调模块化、高内聚、低耦合的设计原则，利用 Rust 的强类型系统和所有权机制确保代码的健壮性、安全性和易于维护性与扩展性。

  程序的名称及可执行程序名称应为PixivTagDownloader， 作者名称为Mannix Sun， 邮箱为***************。

  ## 2. 术语定义

  - **UID:** Pixiv用户的唯一标识符。
  - **PID:** Pixiv作品（图片、插画、漫画、小说）的唯一标识符。
  - **Cookie:** 用于用户身份验证和会话管理的小段文本数据。
  - **Tag:** 用户为作品添加的标签，用于分类和搜索。
  - **元数据 (Metadata):** 描述作品信息的数据，如标题、标签、描述、系列信息、作者、上传日期等。
  - **模块化 (Modularity):** 将程序分解为独立的、可重用的功能单元（Rust 中的 `mod`）。
  - **高内聚 (High Cohesion):** 模块内部的元素（函数、结构体、枚举等）联系紧密，共同完成一个明确的任务。
  - **低耦合 (Low Coupling):** 模块之间的依赖关系尽可能少，一个模块的修改对其他模块的影响最小。
  - **Crate:** Rust 的编译单元和包。
  - **异步 (Asynchronous):** 一种并发编程模型，允许程序在等待I/O操作（如网络请求）完成时执行其他任务，常用 `async/await` 语法。
  - **TOML (Tom's Obvious, Minimal Language):** 一种易于阅读的配置文件格式，在 Rust 生态中广泛使用。

  ## 3. 功能需求 (Functional Requirements)

  ### 3.1 用户认证与会话管理 (FR-AUTH)

  - **FR-AUTH-01:** 程序启动时，必须能够读取指定路径（默认为当前目录，或通过配置文件/命令行参数指定）下名为 `cookie.txt` 的文件。
  - **FR-AUTH-02:** `cookie.txt` 文件格式应为标准的 HTTP Cookie 字符串格式，例如 `key1=value1; key2=value2; ...` (分号后可能跟一个空格，也可能不跟，需兼容)。
  - **FR-AUTH-03:** 程序需使用读取到的Cookie信息模拟登录Pixiv账户，以获取访问用户作品的权限。应能验证Cookie的有效性（例如通过访问一个需要登录的API端点）。
  - **FR-AUTH-04:** 如果 `cookie.txt` 文件不存在、格式不正确或Cookie失效，程序应清晰地提示错误信息，并允许用户通过命令行参数或配置文件指定新的Cookie路径，或安全退出程序。

  ### 3.2 用户交互与作品筛选 (FR-UI)

  - **FR-UI-01:** （交互模式下）程序成功登录后，应提示用户输入目标用户的Pixiv UID。

  - **FR-UI-02:** 程序需验证用户输入的UID格式是否有效（纯数字）。若无效，应提示错误并要求重新输入。

  - **FR-UI-03:** （交互模式下）获取指定UID用户的作品元数据后，程序需能提取这些作品包含的所有唯一Tag。

  - **FR-UI-04:** （交互模式下）程序应首先询问用户是直接输入需要下载的Tag (支持多个Tag，逗号分隔)，还是从该UID用户的所有作品中提取Tag列表供选择。

  - **FR-UI-05:** （交互模式下，若用户选择从列表选择）程序应向用户展示所有提取到的Tag列表，并支持分页或搜索功能（如果Tag过多）。

  - **FR-UI-06:** （交互模式下）程序应允许用户交互式选择一个或多个Tag作为过滤条件。

  - FR-UI-07:

     （交互模式下或通过参数指定）程序应支持用户选择Tag过滤逻辑：

    - **“且” (AND):** 作品需同时包含所有选定Tag。
    - **“或” (OR):** 作品只需包含任意一个选定Tag。
    - **(可选增强) “非” (NOT):** 排除包含指定Tag的作品。

  - **FR-UI-08:** 用户完成Tag选择（或选择下载全部，不进行Tag过滤，或通过命令行参数指定）后，程序应确认下载任务并开始执行。

  - **FR-UI-09:** 在下载过程中，应提供清晰的进度指示（例如，已下载作品数/总作品数，当前文件下载进度）。

  - **FR-UI-10:** 程序应允许用户选择需要下载的作品类型， 可多选或单选。

  ### 3.3 数据获取与处理 (FR-DATA)

  - **FR-DATA-01:** 根据用户输入的UID，程序需要能获取该用户的基本信息，特别是 **用户名 (Username)**，用于后续文件路径和元数据生成。获取到的用户名需进行文件名安全处理。
  - **FR-DATA-02:** 程序需要能获取指定UID用户发布的所有作品列表，包含作品类型（图片、插画、漫画、小说）、PID、标题、标签、系列信息（如果存在）、图片URL（对于图片/漫画，需处理原始高质量图片链接）、小说内容（对于小说）等元数据。此过程可能涉及多次API请求和分页处理。
  - **FR-DATA-03:** 程序需根据用户选择的Tag和过滤逻辑（AND/OR/NOT）过滤作品列表，确定最终需要下载的作品。
  - **FR-DATA-04:** 对于漫画或包含多张图片的作品（同一个PID下有多页），程序需要能获取所有页面的图片URL，并确保下载时顺序正确。
  - **FR-DATA-05:** 获取到的所有文本数据（标题、描述、标签等）应确保正确的UTF-8编码处理。

  ### 3.4 下载功能 (FR-DL)

  - **FR-DL-01:** 程序应使用 Rust 的异步运行时 (如 `tokio` 或 `async-std` 配合 `reqwest` crate) 或并行处理库 (如 `rayon`) 并发下载符合条件的作品，以提高下载效率。并发任务数量应可配置（配置文件或命令行参数），并提供合理的默认值。

  - **FR-DL-02:** 在每次对Pixiv服务器的网络请求（获取作品信息、下载文件等）之间，程序应加入一个随机时间延迟（例如，1-3秒之间，范围可配置），以模拟人类行为，降低被Pixiv服务器阻止的风险。

  - **FR-DL-03:** 下载过程中应优雅地处理可能发生的网络错误（如超时、连接失败、HTTP错误码），并进行可配置的重试（次数和间隔）。无法成功下载的作品应记录错误详情并跳过，不中断整体任务。

  - **FR-DL-04:** 程序应能处理Pixiv可能的反爬虫机制，如需要设置合适的User-Agent、Referer等HTTP头。这些HTTP头应允许用户通过配置文件进行自定义。

  - FR-DL-05:

     允许用户选择下载方式（通过配置文件或命令行参数）：

    - **直接下载:** 使用内置HTTP客户端下载。
    - **Aria2c:** 通过执行外部 `aria2c` 命令行工具下载。程序需能将下载任务参数传递给 `aria2c`。
    - **Aria2 RPC:** 通过JSON-RPC或WebSocket连接到正在运行的Aria2服务进行下载。

  - **FR-DL-06:** 当使用Aria2 RPC方式下载时，程序应支持通过 WSS (WebSocket Secure) 协议连接到Aria2服务，并提供证书验证选项（如信任系统CA、指定自定义CA证书、或选择不验证证书 – 后者需有安全提示）。

  - **FR-DL-07:** (可选增强) 支持断点续传功能，至少对于直接下载方式。

  ### 3.5 文件存储与组织 (FR-STORE)

  - **FR-STORE-01:** 所有下载的内容默认保存在程序运行目录下的 `Output` 文件夹内，但必须允许用户在配置文件或通过命令行参数自定义输出根目录路径。

  - FR-STORE-02:

     应允许用户在配置文件中通过变量组合高度自定义目录结构和文件命名模板。支持的变量应包括但不限于：

    - `{uid}`: Pixiv用户ID
    - `{username}`: Pixiv用户名 (经过文件名安全处理)
    - `{pid}`: 作品ID
    - `{title}`: 作品标题 (经过文件名安全处理)
    - `{type}`: 作品类型 (例如: `Illust`, `Manga`, `Novel`)
    - `{page_index}`: 图片/漫画页码索引号 (例如 `p0`, `p00`, `p000`，格式可配置)
    - `{page_count}`: 作品总页数
    - `{series_title}`: 系列名称 (如果存在，经过文件名安全处理)
    - `{series_id}`: 系列ID (如果存在)
    - `{upload_date}`: 作品上传日期，可指定格式 (例如 `{upload_date:YYYYMMDD}` 或 `{upload_date:%Y-%m-%d}`)
    - `{tags}`: 作品标签 (以指定分隔符连接，例如 `{tags:_}` 或 `{tags: #}`，经过文件名安全处理)
    - `{r18}`: 是否为R18作品 (例如输出 `R18` 或空)
    - `{like_count}`: 点赞数
    - `{bookmark_count}`: 收藏数
    - `{ext}`: 文件原始扩展名 (例如 `.jpg`, `.png`)

  - **FR-STORE-03:** 若用户未自定义路径模板，则提供一个合理的默认目录结构，例如：`Output/{uid}_{username}/{type}/{series_title_or_No_Series}/`。其中 `type` 可以是 `Images` (图片/插画), `Manga` (漫画), `Novels` (小说)。`series_title_or_No_Series` 表示系列名，若无系列则为可配置的默认名（如`_Unsorted`）。

  - **FR-STORE-04:** (已合并入FR-STORE-03的模板化设计)

  - FR-STORE-05 (图片/插画/漫画):

    - **文件保存:** 根据FR-STORE-02的模板规则保存图片文件。
    - **文件命名:** 图片文件命名格式通过模板定义，例如默认为 `{upload_date:%Y%m%d}_{pid}_p{page_index}_{title}.{ext}`。对于单张图片的作品，`{page_index}` 为 `p0` 或类似。文件名中的非法字符需要被自动替换或移除。
    - 元数据文件:
      - 对于**单页作品 (插画)**: 在图片文件旁边创建一个同基本名但扩展名为 `.txt` 的元数据文件 (例如 `artwork.jpg` -> `artwork.txt`)。
      - 对于**多页作品 (漫画/图集)**: 在该作品PID对应的目录（通常由模板定义，例如 `Output/.../{pid}_{title}/`）下，创建一个统一的元数据文件，例如 `_metadata.txt` 或 `{pid}_metadata.txt` (文件名可配置)。
      - 元数据文件内容 (TXT格式，Key: Value 形式):
        - `Title: [作品标题]`
        - `Author_UID: [作者UID]`
        - `Author_Username: [作者用户名]`
        - `Artwork_PID: [作品PID]`
        - `Artwork_Type: [作品类型，例如 Illust, Manga]`
        - `Tags: [tag1, tag2, tag3]` (以逗号和空格分隔，或可配置)
        - `Description: \n[作品描述内容，保留换行]`
        - `Series_Title: [系列标题]` (如果存在)
        - `Series_ID: [系列ID]` (如果存在)
        - `Upload_Date: [上传日期和时间，ISO 8601格式或可配置格式]`
        - `Page_Count: [总页数]`
        - `R18: [True/False]`
        - `Like_Count: [点赞数]`
        - `Bookmark_Count: [收藏数]`
        - `Original_URLs: \n[原始图片URL列表，每行一个]` (可选)
        - `Download_Time: [本次下载完成时间，ISO 8601格式]`

  - FR-STORE-06 (小说):

    - **文件保存:** 小说文件保存位置遵循FR-STORE-02的模板规则。
    - **文件命名:** 小说文件命名格式通过模板定义，默认为 `{upload_date:%Y%m%d}_{pid}_{title}.txt`。文件名中的非法字符需要被自动替换或移除。
    - 文件内容 (TXT格式):
      - `Title: [小说标题]`
      - `Author_UID: [作者UID]`
      - `Author_Username: [作者用户名]`
      - `Novel_PID: [小说PID]`
      - `Novel_Type: Novel`
      - `Upload_Date: [上传日期和时间，ISO 8601格式或可配置格式]`
      - `Tags: [tag1, tag2, tag3]` (以逗号和空格分隔，或可配置)
      - `Series_Title: [系列标题]` (如果存在)
      - `Series_ID: [系列ID]` (如果存在)
      - `R18: [True/False]`
      - `Like_Count: [点赞数]`
      - `Bookmark_Count: [收藏数]`
      - `Word_Count: [字数]`
      - `Description: \n[小说描述内容，保留换行]`
      - `Download_Time: [本次下载完成时间，ISO 8601格式]`
      - `--- Content ---` (分隔符可配置)
      - `\n[小说正文内容，保留原始换行和格式]`

  - FR-STORE-07:

     程序在下载文件前应检查目标路径是否存在同名文件。提供可配置的冲突处理策略：

    - **跳过 (Skip):** 默认行为。
    - **覆盖 (Overwrite):** 覆盖已存在文件。
    - **重命名 (Rename):** 为新文件添加序号 (例如 `file (1).ext`, `file (2).ext`)。

  - **FR-STORE-08:** 文件名和路径中应严格处理操作系统不允许的非法字符（根据Windows、Linux、macOS的规则进行替换或移除，替换字符可配置），并处理路径过长的问题（例如通过截断文件名并提示，或使用部分哈希值缩短路径组件，需确保唯一性）。

  - **FR-STORE-09:** 元数据文件和小说中的文本内容应统一使用UTF-8编码保存。

  ### 3.6 程序执行方式与接口 (FR-EXEC)

  - FR-EXEC-01:

     程序应支持两种不同的执行方式：

    - **交互式操作:** 用户直接执行程序，通过命令行交互界面进行操作，程序引导用户完成各项设置和选择。使用如 `dialoguer` 或类似crate实现。
    - **命令行参数:** 用户可通过命令行参数一次性指定所有必要的操作参数，程序直接执行下载任务而无需交互。使用如 `clap` crate进行参数解析。

  - FR-EXEC-02:

     命令行参数应全面，并能覆盖配置文件中的大部分设置，至少包含：

    - `-u, --uid <UID>`: 指定Pixiv用户ID (必需)。
    - `-t, --tags <TAGS>`: 指定要下载的Tag，多个Tag用逗号分隔。
    - `-l, --logic <LOGIC>`: 指定Tag过滤逻辑，可选值为`and`或`or` (默认为`or`)。
    - `--cookie-file <PATH>`: 指定`cookie.txt`文件路径。
    - `--output-dir <PATH>`: 指定输出根目录。
    - `--config <PATH>`: 指定自定义配置文件路径。
    - `--download-method <METHOD>`: 指定下载方式 (`direct`, `aria2c`, `aria2-rpc`)。
    - `--aria2-rpc-url <URL>`: 指定Aria2 RPC服务地址 (例如 `ws://localhost:6800/jsonrpc`)。
    - `--aria2-rpc-secret <TOKEN>`: 指定Aria2 RPC授权密钥。
    - `--threads <NUM>` 或 `--concurrency <NUM>`: 指定并发下载任务数。
    - `--delay <MIN_MAX_SECONDS>`: 指定随机延迟范围 (例如 `1-3`)。
    - `--skip-existing` / `--overwrite-existing` / `--rename-existing`: 文件冲突处理策略。
    - `--log-level <LEVEL>`: 设置日志级别 (例如 `trace`, `debug`, `info`, `warn`, `error`)。
    - `--all`: 下载用户所有作品，不进行Tag筛选。
    - `--type <type>`: 指定要下载的作品类型（例如 `Illust`, `Manga`, `Novel`, `Illust,Manga`, `all`）， 大小写不敏感， 可单选或多选， 多个类型用逗号分隔， 默认为 `all`。

## 4. 非功能需求 (Non-Functional Requirements)

  - **NFR-DESIGN-01 (模块化):** 代码结构应清晰地划分为不同的模块（例如：`auth`, `api`, `downloader`, `storage`, `config`, `cli`, `core_logic`等），利用Rust的模块系统。
  - **NFR-DESIGN-02 (高内聚):** 每个模块应专注于完成单一的功能（例如：`auth`模块只负责登录和Cookie管理）。
  - **NFR-DESIGN-03 (低耦合):** 模块之间的依赖应最小化，通过定义良好的接口（trait、struct、enum）或标准数据格式进行交互。
  - **NFR-PERF-01 (性能):** 利用 Rust 的异步运行时（如 `tokio`）、并行处理库（如 `rayon`）、以及零成本抽象特性，实现高效并发下载和数据处理。优化内存使用。
  - **NFR-ROBUST-01 (健壮性):** 使用 Rust 的 `Result<T, E>` 和 `Option<T>` 类型进行全面的错误处理，避免 `panic` 在可恢复的错误场景。对 Pixiv API 可能的变更提供一定的兼容性或清晰的错误提示，并记录详细的错误日志。
  - **NFR-SEC-01 (安全性):** 避免硬编码敏感信息。Cookie文件内容不应被随意记录到日志中（除非是Debug级别且有明确提示）。使用Aria2 RPC时，若有密钥，应安全处理。
  - **NFR-USABILITY-01 (易用性):** 交互式命令行界面应清晰、直观，易于用户理解和操作。命令行参数设计应符合常见CLI工具的惯例。错误信息应友好且具有指导性。
  - **NFR-MAINTAIN-01 (可维护性):** 代码应遵循 Rust 社区的编码规范（使用 `rustfmt` 格式化，通过 `clippy` 进行静态检查）。添加完善的文档注释，以便通过 `cargo doc` 生成API文档。代码逻辑清晰，易于理解和修改。
  - **NFR-COMPLIANCE-01 (合规性):** 加入随机延迟，避免对Pixiv服务器造成过大负担。在程序文档和首次运行时，明确提示用户应遵守Pixiv的使用条款，并自行承担下载内容的版权责任。程序仅供个人学习、研究和合法备份用途。
  - **NFR-CONFIG-01 (可配置性):** 程序应支持通过 **TOML** (首选) 或 YAML 格式的配置文件 (`config.toml` 或 `config.yaml`，路径可指定) 自定义各项设置。配置项应包括但不限于FR-STORE、FR-DL、FR-AUTH中提到的可配置项。命令行参数优先级高于配置文件，配置文件高于程序内置默认值。
  - **NFR-PACKAGE-01 (可分发性):** 程序应使用 Cargo 进行构建和管理，方便编译为针对不同平台（Windows, Linux, macOS）的本地可执行文件。如果开源，应易于通过 `cargo install` 或从GitHub Releases获取。
  - **NFR-LOGGING-01 (日志):** 实现分级别的日志系统（例如使用 `log`和`env_logger`或`tracing` crates）。日志级别可通过命令行参数或配置文件设置。日志可输出到控制台和/或指定的文件。日志内容应包含时间戳、级别、模块和消息。
  - **NFR-TEST-01 (可测试性):** 核心逻辑模块（如API交互、数据处理、过滤逻辑）应包含单元测试。关键用户流程（如下载特定UID的作品）应有集成测试。
  - **NFR-I18N-01 (国际化 - 可选未来增强):** 考虑到文档的多语言需求，程序本身的命令行提示信息和日志输出未来可考虑支持国际化 (例如使用 `fluent` crate 或类似机制)。

  ## 5. 输入与输出

  - 输入:
    - `cookie.txt` 文件 (包含有效的Pixiv Cookie)。
    - 用户通过命令行或交互界面输入的 Pixiv UID。
    - 用户通过命令行或交互界面选择的 Tag (或选择不筛选)。
    - 用户通过命令行或交互界面选择的 Tag 过滤逻辑 (AND/OR)。
    - (可选) `config.toml` 配置文件。
    - 其他命令行参数。
  - 输出:
    - 下载的图片/插画/漫画文件 (原始格式，如 JPG, PNG, GIF)。
    - 为每个图片/漫画作品生成的元数据 `.txt` 文件。
    - 下载的小说 `.txt` 文件 (包含元数据和内容)。
    - 按照用户指定规则组织的目录结构。
    - 程序运行过程中的状态信息、进度提示、警告和错误消息（打印到控制台和/或日志文件）。

  ## 6. 约束与假设 (Constraints & Assumptions)

  - **CON-ENV-01:** 程序需要运行在安装了 Rust 工具链 (包括 `cargo` 和 `rustc`，建议最新稳定版) 的环境中才能编译。最终用户只需可执行文件。

  - CON-LIB-01:

     程序将依赖一系列高质量的 Rust crates，例如：

    - **HTTP客户端:** `reqwest` (及其对 `tokio`/`async-std` 的集成)。
    - **异步运行时:** `tokio` (首选) 或 `async-std`。
    - **序列化/反序列化:** `serde`, `serde_json`, `serde_yaml` / `toml`。
    - **HTML/XML解析 (如果需要):** `scraper` 或 `quick-xml`。
    - **命令行参数解析:** `clap`。
    - **交互式CLI:** `dialoguer` (可选)。
    - **进度条:** `indicatif`。
    - **错误处理:** `thiserror`, `anyhow`。
    - **日志:** `log`, `env_logger` 或 `tracing`。
    - **文件系统操作:** 标准库 `std::fs`, `std::path`。

  - **CON-NET-01:** 需要稳定且未受限制的互联网连接才能访问Pixiv。

  - **CON-PIXIV-01:** 程序高度依赖于当前Pixiv网站的结构和其（很可能是非官方的）API接口。Pixiv的任何重大更新（如API变更、反爬虫策略调整）都可能导致程序功能失效或部分失效，需要开发者及时维护更新。**用户应被明确告知此风险。**

  - **CON-COOKIE-01:** 假设用户提供的`cookie.txt`是有效的、未过期的，并且具有访问目标用户作品（包括可能存在的R18内容，若用户期望下载）的权限。程序不负责获取Cookie。

  - **CON-RATE-01:** 随机延迟和并发数量的设置旨在降低风险，但不能100%保证不触发Pixiv的访问频率限制或IP封锁。用户应合理配置这些参数。

  - **CON-ARIA-01:** 使用Aria2功能时，要求用户已正确安装并配置Aria2。当使用WSS协议时，需确保Aria2已正确配置SSL证书。程序将提供选项允许用户选择是否验证服务器证书，或指定自定义CA证书路径。

  - **CON-COMPAT-01:** 程序应力求在主流桌面操作系统（Windows 10/11, macOS, Linux）上表现一致。文件路径和文件名处理需特别注意跨平台兼容性。

  - **CON-LEGAL-01 (法律与合规):** 用户应自行承担使用本程序下载、存储和使用任何受版权保护内容的全部法律责任。程序开发者不对此负责。程序设计初衷仅为个人学习、研究和对已授权内容的合法备份，严禁用于非法传播、商业牟利或任何侵犯版权的行为。

  - **CON-RELEASE-01:** 程序的target应包含 `x86_64-unknown-linux-musl`, `x86_64-pc-windows-gnu`。

  ## 7. 文档要求 (Documentation Requirements)

  - DOC-LANG-01:

     程序完成后需要提供以下语言的完整文档：

    - 英语 (默认，作为主要文档)。
    - 简体中文。
    - 日语。

  - DOC-TYPE-01:

     文档应包括以下内容：

    - **README.md:** 项目概述、特性列表、编译/安装说明、快速上手指南、许可证信息。
    - 用户手册 (User Manual):
      - 详细的使用说明（交互模式和命令行模式）。
      - 所有功能和命令行选项的解释。
      - 配置文件 (`config.toml`) 格式详解及所有可配置项说明。
      - 目录和文件名模板变量详解及示例。
      - Aria2 集成配置指南。
      - 常见问题解答 (FAQ) 和故障排除指南。
      - 使用条款和免责声明。
    - 开发者文档 (Developer Documentation):
      - 通过 `cargo doc` 自动生成的API文档。
      - 项目架构说明。
      - 构建和测试指南。
    - **贡献指南 (CONTRIBUTING.md):** 如何报告问题、提交代码、代码风格要求等。
    - **示例文档 (Examples):** 提供常见使用场景的命令行示例和配置文件片段。

  - **DOC-FORMAT-01:** 所有面向用户的文档（README, User Manual, Examples）应使用 Markdown 格式，托管在项目仓库中（例如 GitHub）。API文档由`cargo doc`生成HTML。